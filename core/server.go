package core

import (
	"fmt"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/flipped-aurora/gin-vue-admin/server/utils/sangfordata"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/initialize"
	"github.com/flipped-aurora/gin-vue-admin/server/service/system"
	"go.uber.org/zap"
)

type server interface {
	ListenAndServe() error
}

func RunWindowsServer() {
	if global.GVA_CONFIG.System.UseMultipoint || global.GVA_CONFIG.System.UseRedis {
		// 初始化redis服务
		initialize.Redis()
	}

	// 从db加载jwt数据
	if global.GVA_DB != nil {
		system.LoadAll()
	}

	Router := initialize.Routers()
	Router.Static("/form-generator", "./resource/page")

	address := fmt.Sprintf(":%d", global.GVA_CONFIG.System.Addr)
	s := initServer(address, Router)
	// 保证文本顺序输出
	// In order to ensure that the text order output can be deleted
	time.Sleep(10 * time.Microsecond)
	global.GVA_LOG.Info("server run success on ", zap.String("address", address))

	fmt.Printf(`
	欢迎使用 电力物联网蜜网系统
	当前版本:v0.1.0
`)
	// go func() {
	// 	cronjobs.SubscribeAttackVideo()
	// }()
	// go func() {
	// 	cronjobs.SubscribeWriteAttackVideoFinish()
	// }()
	go func() {
		utils.ScreenshotReceiverRun()
	}()

	// 启动 syslog_UDP 服务
	go func() {
		sangfordata.SysLogRec()
	}()

	// 启动固件安全事件接收服务 (端口5141)
	go func() {
		sangfordata.SysLogRecFirmware()
	}()

	// 创建定时任务，每天 12点运行一次预警检测
	go func() {
		sangfordata.StartCronJob()
	}()

	global.GVA_LOG.Error(s.ListenAndServe().Error())
}
